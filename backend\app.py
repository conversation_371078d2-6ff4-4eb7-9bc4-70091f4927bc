from flask import Flask
from flask_cors import CORS
from extensions import db
from auth import auth
from admin import admin
from corrections import corrections
import os
from dotenv import load_dotenv
from flask_jwt_extended import JWTManager

# Load environment variables
load_dotenv()

def create_app():
    app = Flask(__name__)

    # Configure the Flask application
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key')
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///ahcorrecting.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'your-jwt-secret-key')

    # File upload configuration
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
    app.config['UPLOAD_FOLDER'] = os.path.join(os.getcwd(), 'uploads')
    app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'tiff', 'bmp'}

    # Create upload directories
    upload_dirs = [
        app.config['UPLOAD_FOLDER'],
        os.path.join(app.config['UPLOAD_FOLDER'], 'student_answers'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'answer_keys'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'reports'),
        os.path.join(app.config['UPLOAD_FOLDER'], 'signatures')
    ]

    for directory in upload_dirs:
        os.makedirs(directory, exist_ok=True)

    # Configure CORS - more permissive for development
    CORS(app, resources={
        r"/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })

    # Initialize extensions
    db.init_app(app)
    jwt = JWTManager(app)

    # Register blueprints
    app.register_blueprint(auth, url_prefix='/api/auth')
    app.register_blueprint(admin, url_prefix='/api/admin')
    app.register_blueprint(corrections, url_prefix='/api/corrections')

    # Create database tables
    with app.app_context():
        db.create_all()

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, port=5000, host='0.0.0.0')
