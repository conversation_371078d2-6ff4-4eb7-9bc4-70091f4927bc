from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import Admin, User, Subject, db
from werkzeug.security import generate_password_hash
from flask_cors import cross_origin

admin = Blueprint('admin', __name__)

# Helper function to check if user is admin
def is_admin():
    user_id = get_jwt_identity()
    admin = Admin.query.get(user_id)
    return admin is not None

# Debug route to check admin users
@admin.route('/debug/admins', methods=['GET'])
@cross_origin()
def debug_admins():
    try:
        admins = Admin.query.all()
        return jsonify([{
            'id': admin.id,
            'name': admin.name,
            'email': admin.email
        } for admin in admins])
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Users routes
@admin.route('/users', methods=['GET'])
@jwt_required()
@cross_origin()
def get_users():
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    users = User.query.all()
    return jsonify([{
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'role': user.role,
        'subjects': [subject.name for subject in user.subjects]
    } for user in users])

@admin.route('/users', methods=['POST'])
@jwt_required()
@cross_origin()
def create_user():
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    
    data = request.get_json()
    if not data or not data.get('name') or not data.get('email') or not data.get('password') or not data.get('role'):
        return jsonify({'error': 'Name, email, password and role are required'}), 400
    
    if data['role'] not in ['teacher', 'student']:
        return jsonify({'error': 'Invalid role. Must be either teacher or student'}), 400
    
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'error': 'Email already registered'}), 400
    
    user = User(
        name=data['name'],
        email=data['email'],
        password_hash=generate_password_hash(data['password']),
        role=data['role']
    )
    
    # Add subjects if provided
    if 'subjects' in data:
        for subject_name in data['subjects']:
            subject = Subject.query.filter_by(name=subject_name).first()
            if subject:
                user.subjects.append(subject)
    
    db.session.add(user)
    db.session.commit()
    
    return jsonify({
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'role': user.role,
        'subjects': [subject.name for subject in user.subjects]
    }), 201

@admin.route('/users/<int:id>', methods=['PUT'])
@jwt_required()
@cross_origin()
def update_user(id):
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    
    user = User.query.get_or_404(id)
    data = request.get_json()
    
    if 'name' in data:
        user.name = data['name']
    if 'email' in data:
        if User.query.filter_by(email=data['email']).first() and user.email != data['email']:
            return jsonify({'error': 'Email already registered'}), 400
        user.email = data['email']
    if 'password' in data:
        user.password_hash = generate_password_hash(data['password'])
    if 'role' in data:
        if data['role'] not in ['teacher', 'student']:
            return jsonify({'error': 'Invalid role. Must be either teacher or student'}), 400
        user.role = data['role']
    
    # Update subjects
    if 'subjects' in data:
        user.subjects = []
        for subject_name in data['subjects']:
            subject = Subject.query.filter_by(name=subject_name).first()
            if subject:
                user.subjects.append(subject)
    
    db.session.commit()
    return jsonify({
        'id': user.id,
        'name': user.name,
        'email': user.email,
        'role': user.role,
        'subjects': [subject.name for subject in user.subjects]
    })

@admin.route('/users/<int:id>', methods=['DELETE'])
@jwt_required()
@cross_origin()
def delete_user(id):
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    user = User.query.get_or_404(id)
    db.session.delete(user)
    db.session.commit()
    return '', 204

# Subjects routes
@admin.route('/subjects', methods=['GET'])
@jwt_required()
@cross_origin()
def get_subjects():
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    subjects = Subject.query.all()
    return jsonify([{
        'id': subject.id,
        'name': subject.name,
        'description': subject.description
    } for subject in subjects])

@admin.route('/subjects', methods=['POST'])
@jwt_required()
@cross_origin()
def create_subject():
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        if not data.get('name'):
            return jsonify({'error': 'Subject name is required'}), 400
        
        name = data['name'].strip()
        if len(name) < 3:
            return jsonify({'error': 'Subject name must be at least 3 characters long'}), 422
        
        if len(name) > 100:
            return jsonify({'error': 'Subject name must be less than 100 characters'}), 422
        
        # Check if subject already exists
        existing_subject = Subject.query.filter_by(name=name).first()
        if existing_subject:
            return jsonify({'error': 'A subject with this name already exists'}), 409
        
        description = data.get('description', '').strip()
        if len(description) > 1000:  # Add reasonable limit for description
            return jsonify({'error': 'Description must be less than 1000 characters'}), 422
        
        try:
            subject = Subject(
                name=name,
                description=description
            )
            
            db.session.add(subject)
            db.session.commit()
            
            return jsonify({
                'id': subject.id,
                'name': subject.name,
                'description': subject.description
            }), 201
            
        except Exception as e:
            db.session.rollback()
            print(f"Database error creating subject: {str(e)}")
            return jsonify({'error': 'Failed to create subject. Please try again.'}), 500
        
    except Exception as e:
        print(f"Error creating subject: {str(e)}")
        return jsonify({'error': 'Invalid request data'}), 422

@admin.route('/subjects/<int:id>', methods=['PUT'])
@jwt_required()
@cross_origin()
def update_subject(id):
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    subject = Subject.query.get_or_404(id)
    data = request.get_json()
    
    if 'name' in data:
        subject.name = data['name']
    if 'description' in data:
        subject.description = data['description']
    
    db.session.commit()
    return jsonify({
        'id': subject.id,
        'name': subject.name,
        'description': subject.description
    })

@admin.route('/subjects/<int:id>', methods=['DELETE'])
@jwt_required()
@cross_origin()
def delete_subject(id):
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    subject = Subject.query.get_or_404(id)
    db.session.delete(subject)
    db.session.commit()
    return '', 204

@admin.route('/debug/db', methods=['GET'])
@jwt_required()
@cross_origin()
def debug_db():
    if not is_admin():
        return jsonify({'error': 'Unauthorized'}), 403
    
    try:
        # Get all data from each table
        students = Student.query.all()
        teachers = Teacher.query.all()
        subjects = Subject.query.all()
        
        return jsonify({
            'students': [{
                'id': s.id,
                'name': s.name,
                'email': s.email,
                'subjects': [sub.name for sub in s.subjects]
            } for s in students],
            'teachers': [{
                'id': t.id,
                'name': t.name,
                'email': t.email,
                'is_admin': t.is_admin,
                'subjects': [sub.name for sub in t.subjects]
            } for t in teachers],
            'subjects': [{
                'id': s.id,
                'name': s.name,
                'description': s.description
            } for s in subjects]
        })
    except Exception as e:
        print(f"Error fetching debug data: {str(e)}")
        return jsonify({'error': str(e)}), 500 