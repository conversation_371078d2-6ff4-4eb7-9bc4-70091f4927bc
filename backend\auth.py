from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from models import User, Admin, db
from werkzeug.security import check_password_hash
from flask_cors import cross_origin

auth = Blueprint('auth', __name__)

@auth.route('/login', methods=['POST'])
@cross_origin()
def login():
    try:
        data = request.get_json()
        if not data or not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Email and password are required'}), 400

        email = data['email']
        password = data['password']
        
        print(f"Login attempt for email: {email}")

        # First check if it's an admin
        admin = Admin.query.filter_by(email=email).first()
        if admin:
            print(f"Found admin user: {admin.email}")
            # Direct password check for debugging
            if check_password_hash(admin.password_hash, password):
                print("Admin password check successful")
                access_token = create_access_token(identity=admin.id)
                return jsonify({
                    'access_token': access_token,
                    'user': {
                        'id': admin.id,
                        'name': admin.name,
                        'email': admin.email,
                        'role': 'admin'
                    }
                })
            else:
                print("Admin password check failed")
                return jsonify({'error': 'Invalid password'}), 401

        # If not admin, check if it's a user (teacher or student)
        user = User.query.filter_by(email=email).first()
        if user:
            print(f"Found regular user: {user.email}")
            if check_password_hash(user.password_hash, password):
                print("User password check successful")
                access_token = create_access_token(identity=user.id)
                return jsonify({
                    'access_token': access_token,
                    'user': {
                        'id': user.id,
                        'name': user.name,
                        'email': user.email,
                        'role': user.role
                    }
                })
            else:
                print("User password check failed")
                return jsonify({'error': 'Invalid password'}), 401

        print("No matching user found")
        return jsonify({'error': 'User not found'}), 401

    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@auth.route('/me', methods=['GET'])
@jwt_required()
@cross_origin()
def get_current_user():
    try:
        user_id = get_jwt_identity()
        print(f"JWT Identity: {user_id}, Type: {type(user_id)}")

        # Check if it's an admin
        admin = Admin.query.get(user_id)
        if admin:
            return jsonify({
                'id': admin.id,
                'name': admin.name,
                'email': admin.email,
                'role': 'admin'
            })

        # Check if it's a user
        user = User.query.get(user_id)
        if user:
            return jsonify({
                'id': user.id,
                'name': user.name,
                'email': user.email,
                'role': user.role
            })

        return jsonify({'error': 'User not found'}), 404
    except Exception as e:
        print(f"Error in get_current_user: {str(e)}")
        return jsonify({'error': f'Internal error: {str(e)}'}), 500